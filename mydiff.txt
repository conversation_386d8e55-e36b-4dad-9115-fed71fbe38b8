diff --git a/article2db/src/main/java/org/biosino/lf/pds/article/service/impl/ArticleServiceImpl.java b/article2db/src/main/java/org/biosino/lf/pds/article/service/impl/ArticleServiceImpl.java
index a907739..925d7ca 100644
--- a/article2db/src/main/java/org/biosino/lf/pds/article/service/impl/ArticleServiceImpl.java
+++ b/article2db/src/main/java/org/biosino/lf/pds/article/service/impl/ArticleServiceImpl.java
@@ -47,9 +47,9 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
     private final IArticleGrantService articleGrantService;
     private final IArticleOtherIdService articleOtherIdService;
     private final IReferenceService referenceService;
-    private final IAuthorService authorService;
-    private final IOrganizationService organizationService;
-    private final IArticleAuthorService articleAuthorService;
+    // private final IAuthorService authorService;
+    // private final IOrganizationService organizationService;
+    // private final IArticleAuthorService articleAuthorService;
 
     @Override
     @Transactional(rollbackFor = Exception.class)
@@ -140,26 +140,26 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
 
         // 作者优先采用Pubmed的
         // 删除PMC的作者信息 和相关联的组织
-        articleAuthorService.removeByDocId(pmcArticle.getId());
-
-        List<Long> removeAuthorIds = new ArrayList<>();
-        List<Long> removeOrganizationIds = new ArrayList<>();
-
-        for (ArticleAuthor articleAuthor : pmcArticleDTO.getArticleAuthors()) {
-            removeAuthorIds.add(articleAuthor.getAuthorId());
-            if (CollUtil.isNotEmpty(articleAuthor.getOrganizations())) {
-                for (Organization organization : articleAuthor.getOrganizations()) {
-                    removeOrganizationIds.add(organization.getId());
-                }
-            }
-        }
-
-        if (CollUtil.isNotEmpty(removeAuthorIds)) {
-            authorService.removeBatchByIds(removeAuthorIds.stream().sorted().collect(Collectors.toList()));
-        }
-        if (CollUtil.isNotEmpty(removeOrganizationIds)) {
-            organizationService.removeBatchByIds(removeOrganizationIds.stream().sorted().collect(Collectors.toList()));
-        }
+        // articleAuthorService.removeByDocId(pmcArticle.getId());
+        //
+        // List<Long> removeAuthorIds = new ArrayList<>();
+        // List<Long> removeOrganizationIds = new ArrayList<>();
+        //
+        // for (ArticleAuthor articleAuthor : pmcArticleDTO.getArticleAuthors()) {
+        //     removeAuthorIds.add(articleAuthor.getAuthorId());
+        //     if (CollUtil.isNotEmpty(articleAuthor.getOrganizations())) {
+        //         for (Organization organization : articleAuthor.getOrganizations()) {
+        //             removeOrganizationIds.add(organization.getId());
+        //         }
+        //     }
+        // }
+        //
+        // if (CollUtil.isNotEmpty(removeAuthorIds)) {
+        //     authorService.removeBatchByIds(removeAuthorIds.stream().sorted().collect(Collectors.toList()));
+        // }
+        // if (CollUtil.isNotEmpty(removeOrganizationIds)) {
+        //     organizationService.removeBatchByIds(removeOrganizationIds.stream().sorted().collect(Collectors.toList()));
+        // }
 
         // 删除pmc的reference，采用pubmed reference
         referenceService.removeByDocId(pmcArticle.getId());
@@ -449,7 +449,8 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
             referenceService.saveBatch(references);
         }
 
-        // 保存Author和ArticleAuthor
+        // 保存Author和ArticleAuthor（原有逻辑，已注释）
+        /*
         Set<ArticleAuthor> articleAuthors = dto.getArticleAuthors();
         if (CollUtil.isNotEmpty(articleAuthors)) {
             // 使用更可靠的标识符作为键，比如作者姓名或其他唯一标识
@@ -518,6 +519,46 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
 
             articleAuthorService.saveBatch(articleAuthors);
         }
+        */
+
+        // 新的作者信息处理逻辑：转换为JSON格式存储
+        Set<ArticleAuthor> articleAuthors = dto.getArticleAuthors();
+        if (CollUtil.isNotEmpty(articleAuthors)) {
+            List<AuthorInfo> authorInfoList = new ArrayList<>();
+
+            for (ArticleAuthor articleAuthor : articleAuthors) {
+                AuthorInfo authorInfo = new AuthorInfo();
+
+                // 设置作者基本信息
+                if (articleAuthor.getAuthor() != null) {
+                    Author author = articleAuthor.getAuthor();
+                    authorInfo.setForename(author.getForename());
+                    authorInfo.setLastname(author.getLastname());
+                    authorInfo.setEmail(author.getEmail());
+                    authorInfo.setType(author.getType());
+                }
+
+                // 设置作者顺序和通讯作者信息
+                authorInfo.setAuthorOrder(articleAuthor.getAuthorOrder());
+                authorInfo.setAuthorCorrespond(articleAuthor.getAuthorCorrespond());
+
+                // 设置机构信息
+                if (articleAuthor.getOrganizations() != null && !articleAuthor.getOrganizations().isEmpty()) {
+                    List<String> orgNames = articleAuthor.getOrganizations().stream()
+                            .map(Organization::getName)
+                            .collect(Collectors.toList());
+                    authorInfo.setOrganizations(orgNames);
+                }
+
+                authorInfoList.add(authorInfo);
+            }
+
+            // 将作者信息设置到Article对象中
+            article.setAuthorInfo(authorInfoList);
+
+            // 更新Article记录以保存作者信息
+            this.updateById(article);
+        }
 
         // 合并PubMed和PMC中的同一篇文献
         merge(article.getPmid(), article.getPmcId());
@@ -577,28 +618,28 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
         referenceService.removeByDocId(docId);
 
         // 删除作者关联信息和未使用的作者、机构
-        List<ArticleAuthor> articleAuthors = articleAuthorService.findByDocId(docId);
-        List<Long> authorIds = articleAuthors.stream().map(ArticleAuthor::getAuthorId).collect(Collectors.toList());
-
-        // 修改此处逻辑，适应 organizationId 作为 List<Long> 的情况
-        Set<Long> organizationIdsSet = new HashSet<>();
-        for (ArticleAuthor author : articleAuthors) {
-            if (author.getOrganizationId() != null) {
-                organizationIdsSet.addAll(author.getOrganizationId());
-            }
-        }
-        List<Long> organizationIds = new ArrayList<>(organizationIdsSet);
-
-        if (CollUtil.isNotEmpty(authorIds)) {
-            // 排序后删除，避免高并发删除时死锁
-            authorService.removeBatchByIds(authorIds.stream().sorted().collect(Collectors.toList()));
-        }
-
-        if (CollUtil.isNotEmpty(organizationIds)) {
-            organizationService.removeBatchByIds(organizationIds.stream().sorted().collect(Collectors.toList()));
-        }
-        // 最后才能删除articleAuthor中间表
-        articleAuthorService.removeByDocId(docId);
+        // List<ArticleAuthor> articleAuthors = articleAuthorService.findByDocId(docId);
+        // List<Long> authorIds = articleAuthors.stream().map(ArticleAuthor::getAuthorId).collect(Collectors.toList());
+        //
+        // // 修改此处逻辑，适应 organizationId 作为 List<Long> 的情况
+        // Set<Long> organizationIdsSet = new HashSet<>();
+        // for (ArticleAuthor author : articleAuthors) {
+        //     if (author.getOrganizationId() != null) {
+        //         organizationIdsSet.addAll(author.getOrganizationId());
+        //     }
+        // }
+        // List<Long> organizationIds = new ArrayList<>(organizationIdsSet);
+        //
+        // if (CollUtil.isNotEmpty(authorIds)) {
+        //     // 排序后删除，避免高并发删除时死锁
+        //     authorService.removeBatchByIds(authorIds.stream().sorted().collect(Collectors.toList()));
+        // }
+        //
+        // if (CollUtil.isNotEmpty(organizationIds)) {
+        //     organizationService.removeBatchByIds(organizationIds.stream().sorted().collect(Collectors.toList()));
+        // }
+        // // 最后才能删除articleAuthor中间表
+        // articleAuthorService.removeByDocId(docId);
     }
 
     public ArticleDTO obtainArticleDTO(Long id) {
@@ -662,8 +703,8 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
         List<Reference> referenceList = referenceService.findByDocId(id);
         articleDTO.setReferences(referenceList);
 
-        List<ArticleAuthor> articleAuthorList = articleAuthorService.findByDocId(id);
-        articleDTO.setArticleAuthors(articleAuthorList);
+        // List<ArticleAuthor> articleAuthorList = articleAuthorService.findByDocId(id);
+        // articleDTO.setArticleAuthors(articleAuthorList);
 
         return articleDTO;
     }
diff --git a/pds-common/src/main/java/org/biosino/lf/pds/article/config/AuthorInfoListTypeHandler.java b/pds-common/src/main/java/org/biosino/lf/pds/article/config/AuthorInfoListTypeHandler.java
index c15d519..51897d9 100644
--- a/pds-common/src/main/java/org/biosino/lf/pds/article/config/AuthorInfoListTypeHandler.java
+++ b/pds-common/src/main/java/org/biosino/lf/pds/article/config/AuthorInfoListTypeHandler.java
@@ -1,64 +1,17 @@
 package org.biosino.lf.pds.article.config;
 
-import com.fasterxml.jackson.core.JsonProcessingException;
-import com.fasterxml.jackson.core.type.TypeReference;
-import com.fasterxml.jackson.databind.ObjectMapper;
-import org.apache.ibatis.type.BaseTypeHandler;
-import org.apache.ibatis.type.JdbcType;
+import com.alibaba.fastjson2.TypeReference;
 import org.biosino.lf.pds.article.domain.AuthorInfo;
-import org.postgresql.util.PGobject;
 
-import java.sql.CallableStatement;
-import java.sql.PreparedStatement;
-import java.sql.ResultSet;
-import java.sql.SQLException;
 import java.util.List;
 
-/**
- * AuthorInfo列表的JSON类型处理器
- */
-public class AuthorInfoListTypeHandler extends BaseTypeHandler<List<AuthorInfo>> {
 
-    private static final ObjectMapper objectMapper = new ObjectMapper();
+public class AuthorInfoListTypeHandler extends ListTypeHandler<AuthorInfo> {
 
     @Override
-    public void setNonNullParameter(PreparedStatement ps, int i, List<AuthorInfo> parameter, JdbcType jdbcType) throws SQLException {
-        try {
-            PGobject jsonObject = new PGobject();
-            jsonObject.setType("jsonb");
-            jsonObject.setValue(objectMapper.writeValueAsString(parameter));
-            ps.setObject(i, jsonObject);
-        } catch (JsonProcessingException e) {
-            throw new SQLException("Error converting AuthorInfo list to JSON", e);
-        }
+    protected TypeReference<List<AuthorInfo>> specificType() {
+        return new TypeReference<List<AuthorInfo>>() {
+        };
     }
 
-    @Override
-    public List<AuthorInfo> getNullableResult(ResultSet rs, String columnName) throws SQLException {
-        String json = rs.getString(columnName);
-        return parseJson(json);
-    }
-
-    @Override
-    public List<AuthorInfo> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
-        String json = rs.getString(columnIndex);
-        return parseJson(json);
-    }
-
-    @Override
-    public List<AuthorInfo> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
-        String json = cs.getString(columnIndex);
-        return parseJson(json);
-    }
-
-    private List<AuthorInfo> parseJson(String json) throws SQLException {
-        if (json == null || json.trim().isEmpty()) {
-            return null;
-        }
-        try {
-            return objectMapper.readValue(json, new TypeReference<List<AuthorInfo>>() {});
-        } catch (JsonProcessingException e) {
-            throw new SQLException("Error parsing JSON to AuthorInfo list", e);
-        }
-    }
 }
diff --git a/pds-common/src/main/java/org/biosino/lf/pds/article/domain/Article.java b/pds-common/src/main/java/org/biosino/lf/pds/article/domain/Article.java
index 840f2af..4369fbf 100644
--- a/pds-common/src/main/java/org/biosino/lf/pds/article/domain/Article.java
+++ b/pds-common/src/main/java/org/biosino/lf/pds/article/domain/Article.java
@@ -6,6 +6,7 @@ import com.baomidou.mybatisplus.annotation.TableId;
 import com.baomidou.mybatisplus.annotation.TableName;
 import com.fasterxml.jackson.annotation.JsonFormat;
 import lombok.Data;
+import org.biosino.lf.pds.article.config.AuthorInfoListTypeHandler;
 import org.biosino.lf.pds.article.config.PubMedPubDateListTypeHandler;
 import org.biosino.lf.pds.article.config.StringListArrayTypeHandler;
 
@@ -225,4 +226,10 @@ public class Article {
     @TableField(exist = false)
     private List<ArticleAuthor> articleAuthors;
 
+    /**
+     * 作者信息JSON数组（新的存储方式）
+     */
+    @TableField(value = "author_info", typeHandler = AuthorInfoListTypeHandler.class)
+    private List<AuthorInfo> authorInfo;
+
 }
diff --git a/pds-common/src/main/resources/mapper/article/ArticleMapper.xml b/pds-common/src/main/resources/mapper/article/ArticleMapper.xml
index 3fd0467..83bb8ce 100644
--- a/pds-common/src/main/resources/mapper/article/ArticleMapper.xml
+++ b/pds-common/src/main/resources/mapper/article/ArticleMapper.xml
@@ -36,6 +36,8 @@
                 typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
         <result property="affiliation" column="affiliation"
                 typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
+        <result property="authorInfo" column="author_info"
+                typeHandler="org.biosino.lf.pds.article.config.AuthorInfoListTypeHandler"/>
         <result property="keywords" column="keywords"
                 typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
         <result property="otherDate" column="other_date"
@@ -66,6 +68,7 @@
                a.page,
                a.author,
                a.affiliation,
+               a.author_info,
                a.keywords,
                a.abstract,
                a.other_abstract,
diff --git a/pds-system/src/main/java/org/biosino/lf/pds/article/service/impl/ArticleServiceImpl.java b/pds-system/src/main/java/org/biosino/lf/pds/article/service/impl/ArticleServiceImpl.java
index 89cbef0..3131c11 100644
--- a/pds-system/src/main/java/org/biosino/lf/pds/article/service/impl/ArticleServiceImpl.java
+++ b/pds-system/src/main/java/org/biosino/lf/pds/article/service/impl/ArticleServiceImpl.java
@@ -16,7 +16,9 @@ import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
 import org.biosino.lf.pds.article.domain.*;
 import org.biosino.lf.pds.article.dto.*;
 import org.biosino.lf.pds.article.mapper.ArticleMapper;
-import org.biosino.lf.pds.article.service.*;
+import org.biosino.lf.pds.article.service.IArticleService;
+import org.biosino.lf.pds.article.service.IJournalService;
+import org.biosino.lf.pds.article.service.ITbDdsFileService;
 import org.biosino.lf.pds.article.vo.ErrorMsgVO;
 import org.biosino.lf.pds.common.constant.DirConstants;
 import org.biosino.lf.pds.common.enums.task.ArticleAttachmentSourceEnum;
@@ -43,11 +45,9 @@ import java.util.stream.Collectors;
 public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements IArticleService {
 
 
-    private final IArticleAuthorService articleAuthorService;
-
-    private final IOrganizationService organizationService;
-
-    private final IAuthorService authorService;
+    // private final IArticleAuthorService articleAuthorService;
+    // private final IOrganizationService organizationService;
+    // private final IAuthorService authorService;
 
     private final ITbDdsFileService fileService;
     private final IJournalService journalService;
@@ -101,8 +101,9 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
     public Article selectArticleById(Long id) {
         Article article = baseMapper.selectArticleById(id);
 
-        List<ArticleAuthor> authors = articleAuthorService.findByDocId(article.getId());
-        article.setArticleAuthors(authors);
+        // 移除ArticleAuthor查询，现在使用Article.authorInfo字段
+        // List<ArticleAuthor> authors = articleAuthorService.findByDocId(article.getId());
+        // article.setArticleAuthors(authors);
 
         List<TbDdsFile> pdfList = fileService.findByDocIdAndType(article.getId(), FileTypeEnum.PDF.name());
         if (CollUtil.isNotEmpty(pdfList)) {
@@ -122,64 +123,33 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
     public void updateArticle(ArticleUpdateDTO articleUpdateDTO) {
         Article article = selectArticleById(articleUpdateDTO.getId());
 
-        List<String> organizations = articleUpdateDTO.getOrganizations();
-
-        LinkedHashMap<String, Long> orgNameToOrgIdMap = new LinkedHashMap<>();
-
-        List<Organization> saveOrgs = new ArrayList<>();
-        List<ArticleAuthor> saveArticleAuthors = new ArrayList<>();
-        List<Author> saveAuthors = new ArrayList<>();
-        for (String orgName : organizations) {
-            Organization org = new Organization();
-            long orgId = IdUtil.getSnowflakeNextId();
-            orgNameToOrgIdMap.put(orgName, orgId);
-            org.setId(orgId);
-            org.setName(orgName);
-            saveOrgs.add(org);
-        }
+        // 拷贝Article基本信息
+        BeanUtil.copyProperties(articleUpdateDTO, article);
 
+        // 构建AuthorInfo列表
         List<AuthorDTO> authorList = articleUpdateDTO.getAuthorList();
+        if (CollUtil.isNotEmpty(authorList)) {
+            List<AuthorInfo> authorInfoList = new ArrayList<>();
+
+            int authorOrder = 1;
+            for (AuthorDTO authorDTO : authorList) {
+                AuthorInfo authorInfo = new AuthorInfo();
+                authorInfo.setForename(authorDTO.getForename());
+                authorInfo.setLastname(authorDTO.getLastname());
+                authorInfo.setEmail(authorDTO.getEmail());
+                authorInfo.setType("person"); // 默认类型为person
+                authorInfo.setAuthorOrder(authorOrder++);
+                authorInfo.setOrganizations(authorDTO.getOrganizations());
+
+                authorInfoList.add(authorInfo);
+            }
 
-        int authorOrder = 1;
-        for (AuthorDTO authorDTO : authorList) {
-            ArticleAuthor articleAuthor = new ArticleAuthor();
-            articleAuthor.setId(IdUtil.getSnowflakeNextId());
-            articleAuthor.setDocId(article.getId());
-
-            Author author = new Author();
-            long authorId = IdUtil.getSnowflakeNextId();
-            author.setId(authorId);
-            author.setForename(authorDTO.getForename());
-            author.setLastname(authorDTO.getLastname());
-            author.setEmail(authorDTO.getEmail());
-            saveAuthors.add(author);
-
-            articleAuthor.setAuthorId(authorId);
-            articleAuthor.setAuthorOrder(authorOrder++);
-            articleAuthor.setOrganizationId(authorDTO.getOrganizations().stream().map(orgNameToOrgIdMap::get).collect(Collectors.toList()));
-
-            saveArticleAuthors.add(articleAuthor);
+            // 设置作者信息到Article对象
+            article.setAuthorInfo(authorInfoList);
         }
 
-        // 拷贝Article基本信息
-        BeanUtil.copyProperties(articleUpdateDTO, article);
-
-
-        // 删除原来的数据
-        List<ArticleAuthor> articleAuthors = article.getArticleAuthors();
-        List<Long> deleteAuthorIds = articleAuthors.stream().map(x -> x.getAuthor().getId()).toList();
-        List<Long> deleteOrgIds = articleAuthors.stream().flatMap(x -> x.getOrganizationId().stream()).toList();
-
-        // articleAuthorService.removeByDocId(article.getId());
-        // authorService.removeByIds(deleteAuthorIds);
-        // organizationService.removeByIds(deleteOrgIds);
-        //
-        // // 保存新的数据
-        // articleAuthorService.saveBatch(saveArticleAuthors);
-        // authorService.saveBatch(saveAuthors);
-        // organizationService.saveBatch(saveOrgs);
-        //
-        // this.save(article);
+        // 更新文章信息
+        this.updateById(article);
     }
 
     @Override
@@ -673,7 +643,7 @@ public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> impl
                 .lt(Article::getCustomId, maxValue)
                 .orderByDesc(Article::getCustomId)
                 .last("LIMIT 1"));
-        return article != null ? article.getCustomId() : null;
+        return article != null ? article.getCustomId() : minValue;
     }
 
     @Override
diff --git a/plosp-admin-ui/src/views/literature/index.vue b/plosp-admin-ui/src/views/literature/index.vue
index 72131c6..1c02683 100644
--- a/plosp-admin-ui/src/views/literature/index.vue
+++ b/plosp-admin-ui/src/views/literature/index.vue
@@ -146,7 +146,7 @@
 
         <!-- 基本信息 -->
         <el-descriptions-item label="作者" :span="2">
-          {{ formatArticleAuthors(detailData.articleAuthors) || '-' }}
+          {{ formatArticleAuthors(detailData.authorInfo) || '-' }}
         </el-descriptions-item>
         <el-descriptions-item label="单位" :span="2">
           {{ formatAffiliations(detailData.affiliation) || '-' }}
@@ -458,16 +458,15 @@
 </template>
 
 <script setup name="Article">
-import {Document, UploadFilled, Select} from '@element-plus/icons-vue';
+import {Document, UploadFilled} from '@element-plus/icons-vue';
 import Editor from '@/components/Editor/index.vue';
-import {deleteAttachment, getArticle, listArticle} from "@/api/article/article.js";
+import {deleteAttachment, getArticle, listArticle, updateArticle} from "@/api/article/article.js";
 import {listJournal} from "@/api/article/journal.js";
 import {debounce, isStrBlank} from "@/utils/index.js";
 import {getToken} from '@/utils/auth';
 import {computed} from 'vue';
 import {useRouter} from 'vue-router';
 import {downloadUseForm} from "@/utils/download.js";
-import axios from 'axios';
 
 
 const router = useRouter();
@@ -871,25 +870,32 @@ function handleEdit(row) {
     let orgId = new Set()
     let authorList = [];
 
-    if (data.articleAuthors) {
-      data.articleAuthors.forEach(it => {
-        let author = it.author;
-        let authorOrg = it.organizations;
-        authorOrg.forEach(it => {
-          if (!orgId.has(it.id)) {
-            organizations.push({
-              id: it.id,
-              name: it.name
-            })
-            orgId.add(it.id)
-          }
-        })
+    // 从authorInfo字段加载数据
+    if (data.authorInfo) {
+      data.authorInfo.forEach(authorInfo => {
+        // 提取机构信息
+        if (authorInfo.organizations) {
+          authorInfo.organizations.forEach(orgName => {
+            if (!orgId.has(orgName)) {
+              organizations.push({
+                id: generateRandomId(),
+                name: orgName
+              })
+              orgId.add(orgName)
+            }
+          })
+        }
+
+        // 构建作者信息
         authorList.push({
-          id: author.id,
-          lastname: author.lastname,
-          forename: author.forename,
-          email: author.email,
-          organizationIds: authorOrg.map(org => org.id)
+          lastname: authorInfo.lastname,
+          forename: authorInfo.forename,
+          email: authorInfo.email,
+          organizationIds: authorInfo.organizations ? authorInfo.organizations.map(orgName => {
+            // 找到对应的组织ID
+            const org = organizations.find(o => o.name === orgName);
+            return org ? org.id : generateRandomId();
+          }) : []
         })
       })
     }
@@ -1057,17 +1063,15 @@ function submitEdit() {
       orgMap.set(org.id, org.name)
     })
 
-    // 使用过滤后的作者数据
+    // 使用过滤后的作者数据，组装成AuthorDTO格式
     filteredAuthorList.forEach(author => {
       // 过滤掉无效的组织关联（组织已被删除或为空）
       const validOrgIds = author.organizationIds.filter(orgId => orgMap.has(orgId));
 
       authorList.push({
-        ...author,
-        lastname: author.lastname,
         forename: author.forename,
+        lastname: author.lastname,
         email: isStrBlank(author.email) ? null : author.email,
-        organizationIds: validOrgIds,
         organizations: validOrgIds.map(orgId => orgMap.get(orgId))
       })
     })
@@ -1081,16 +1085,15 @@ function submitEdit() {
     console.log('提交的数据:');
     console.log(submitData)
 
-    // 在实际应用中，应该调用API保存文献
-    // updateArticle(submitData).then(response => {
-    //   proxy.$modal.msgSuccess('修改成功');
-    //   editDialogVisible.value = false;
-    //   getArticleList();
-    // });
-
-    // 模拟保存成功
-    proxy.$modal.msgSuccess('修改成功');
-    editDialogVisible.value = false;
+    // 调用API保存文献
+    updateArticle(submitData).then(response => {
+      proxy.$modal.msgSuccess('修改成功');
+      editDialogVisible.value = false;
+      getArticleList();
+    }).catch(error => {
+      console.error('保存失败:', error);
+      proxy.$modal.msgError('保存失败，请重试');
+    });
   });
 }
 
@@ -1100,16 +1103,18 @@ function cancelEdit() {
   proxy.$refs.editFormRef?.resetFields();
 }
 
-// 修改格式化函数
-function formatArticleAuthors(articleAuthors) {
-  if (!articleAuthors || articleAuthors.length === 0) return '-';
-
-  return articleAuthors.map(item => {
-    const author = item.author || {};
-    const lastname = isStrBlank(author.lastname) ? '' : author.lastname;
-    const forename = isStrBlank(author.forename) ? '' : author.forename;
-    return `${lastname} ${forename}`.trim();
-  }).join(', ');
+// 修改格式化函数 - 支持新的authorInfo格式
+function formatArticleAuthors(data) {
+  // 优先使用authorInfo字段
+  if (data && data.length > 0) {
+    // 检查是否是新的authorInfo格式
+    return data.map(authorInfo => {
+      const lastname = isStrBlank(authorInfo.lastname) ? '' : authorInfo.lastname;
+      const forename = isStrBlank(authorInfo.forename) ? '' : authorInfo.forename;
+      return `${lastname} ${forename}`.trim();
+    }).join(', ');
+  }
+  return '-';
 }
 
 function formatAffiliations(affiliations) {
