package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.article.config.AuthorInfoListTypeHandler;
import org.biosino.lf.pds.article.config.PubMedPubDateListTypeHandler;
import org.biosino.lf.pds.article.config.StringListArrayTypeHandler;

import java.util.Date;
import java.util.List;

/**
 * 文章信息表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_article", autoResultMap = true)
public class Article {
    /**
     * 文档ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 自定义ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("custom_id")
    private Long customId;

    /**
     * PubMed ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("pmid")
    private Long pmid;

    /**
     * PMC ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField("pmc_id")
    private Long pmcId;

    /**
     * DOI
     */
    @TableField("doi")
    private String doi;

    /**
     * 来源（PubMed、PMC、BioRxiv、MedRxiv、自定义）
     */
    @TableField(value = "source", typeHandler = StringListArrayTypeHandler.class)
    private List<String> source;

    /**
     * 发布状态
     */
    @TableField("pub_status")
    private String pubStatus;

    /**
     * 语言
     */
    @TableField("language")
    private String language;

    /**
     * 本地标题
     */
    @TableField("vernacular_title")
    private String vernacularTitle;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 发布年份
     */
    @TableField("published_year")
    private Integer publishedYear;

    /**
     * 发布月份
     */
    @TableField("published_month")
    private Integer publishedMonth;

    /**
     * 发布日期
     */
    @TableField("published_day")
    private Integer publishedDay;

    /**
     * 其他日期（received、revised、accepted、epub、ppub）
     */
    @TableField(value = "other_date", typeHandler = PubMedPubDateListTypeHandler.class)
    private List<PubMedPubDate> otherDate;

    /**
     * 期刊ID
     */
    @TableField("journal_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long journalId;

    @TableField(exist = false)
    private String journalName;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 卷号
     */
    @TableField("volume")
    private String volume;

    /**
     * 期号
     */
    @TableField("issue")
    private String issue;

    /**
     * 页码
     */
    @TableField("page")
    private String page;

    /**
     * 作者列表
     */
    @TableField(value = "author", typeHandler = StringListArrayTypeHandler.class)
    private List<String> author;

    /**
     * 机构列表
     */
    @TableField(value = "affiliation", typeHandler = StringListArrayTypeHandler.class)
    private List<String> affiliation;

    /**
     * 关键词列表
     */
    @TableField(value = "keywords", typeHandler = StringListArrayTypeHandler.class)
    private List<String> keywords;

    /**
     * 摘要
     */
    @TableField("abstract")
    private String articleAbstract;

    /**
     * 其他摘要
     */
    @TableField("other_abstract")
    private String otherAbstract;

    /**
     * 版权信息
     */
    @TableField("copyright")
    private String copyright;

    /**
     * 点击数
     */
    @TableField("hit_num")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long hitNum = 0L;

    /**
     * 下载数
     */
    @TableField("download")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long download = 0L;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    @TableField(exist = false)
    private Boolean existPdf;

    /**
     * PDF文件信息
     */
    @TableField(exist = false)
    private TbDdsFile pdfFile;

    /**
     * 补充文件信息列表
     */
    @TableField(exist = false)
    private List<TbDdsFile> suppFiles;

    /**
     * 文章关联的作者信息
     */
    @TableField(exist = false)
    private List<ArticleAuthor> articleAuthors;

    /**
     * 作者信息JSON数组（新的存储方式）
     */
    @TableField(value = "author_info", typeHandler = AuthorInfoListTypeHandler.class)
    private List<AuthorInfo> authorInfo;

}
